{"version": 3, "sources": ["../bundle-NR7irB/checked-fetch.js", "../../../src/worker.js", "file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-NR7irB/middleware-insertion-facade.js", "file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/common.ts", "../bundle-NR7irB/middleware-loader.entry.ts"], "sourceRoot": "Z:\\my\\vue-project - 普通版本\\.wrangler\\tmp\\dev-mUfKZ0", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "// <PERSON>flare Worker for Bookmarks API\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n    const path = url.pathname;\n\n    // CORS 头设置\n    const corsHeaders = {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    };\n\n    // 处理 OPTIONS 请求（预检请求）\n    if (request.method === 'OPTIONS') {\n      return new Response(null, { headers: corsHeaders });\n    }\n\n    try {\n      // API 路由\n      if (path.startsWith('/api/bookmarks')) {\n        return await handleBookmarksAPI(request, env, corsHeaders);\n      }\n\n      // 留言 API 路由\n      if (path.startsWith('/api/messages')) {\n        return await handleMessagesAPI(request, env, corsHeaders);\n      }\n\n      return new Response('Not Found', { status: 404, headers: corsHeaders });\n    } catch (error) {\n      console.error('Worker error:', error);\n      return new Response(JSON.stringify({ error: 'Internal Server Error' }), {\n        status: 500,\n        headers: { ...corsHeaders, 'Content-Type': 'application/json' }\n      });\n    }\n  }\n};\n\n// 处理书签 API 请求\nasync function handleBookmarksAPI(request, env, corsHeaders) {\n  const url = new URL(request.url);\n  const method = request.method;\n  const pathParts = url.pathname.split('/');\n\n  const headers = {\n    ...corsHeaders,\n    'Content-Type': 'application/json'\n  };\n\n  switch (method) {\n    case 'GET':\n      // 获取所有书签\n      if (pathParts[3] === undefined) {\n        return await getAllBookmarks(env, headers);\n      }\n      // 获取单个书签\n      else {\n        const id = pathParts[3];\n        return await getBookmark(env, id, headers);\n      }\n\n    case 'POST':\n      // 添加新书签\n      return await addBookmark(request, env, headers);\n\n    case 'PUT':\n      // 更新书签\n      const updateId = pathParts[3];\n      return await updateBookmark(request, env, updateId, headers);\n\n    case 'DELETE':\n      // 删除书签\n      const deleteId = pathParts[3];\n      return await deleteBookmark(request, env, deleteId, headers);\n\n    default:\n      return new Response(JSON.stringify({ error: 'Method not allowed' }), {\n        status: 405,\n        headers\n      });\n  }\n}\n\n// 获取所有书签\nasync function getAllBookmarks(env, headers) {\n  try {\n    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');\n    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: bookmarks\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to fetch bookmarks'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 获取单个书签\nasync function getBookmark(env, id, headers) {\n  try {\n    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');\n    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];\n    const bookmark = bookmarks.find(b => b.id === parseInt(id));\n\n    if (!bookmark) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Bookmark not found'\n      }), {\n        status: 404,\n        headers\n      });\n    }\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: bookmark\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to fetch bookmark'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 添加新书签\nasync function addBookmark(request, env, headers) {\n  try {\n    // 验证管理员权限\n    const authResult = await verifyAdmin(request, env);\n    if (!authResult.success) {\n      return new Response(JSON.stringify(authResult), {\n        status: 401,\n        headers\n      });\n    }\n\n    const newBookmark = await request.json();\n\n    // 验证必填字段\n    if (!newBookmark.name || !newBookmark.url || !newBookmark.description) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Missing required fields: name, url, description'\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');\n    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];\n\n    // 生成新 ID\n    const newId = bookmarks.length > 0 ? Math.max(...bookmarks.map(b => b.id)) + 1 : 1;\n\n    const bookmark = {\n      id: newId,\n      name: newBookmark.name,\n      description: newBookmark.description,\n      url: newBookmark.url,\n      icon: newBookmark.icon || '🔗',\n      category: newBookmark.category || '其他',\n      color: newBookmark.color || '#00d4ff',\n      createdAt: new Date().toISOString()\n    };\n\n    bookmarks.push(bookmark);\n    await env.BOOKMARKS_KV.put('bookmarks', JSON.stringify(bookmarks));\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: bookmark\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to add bookmark'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 更新书签\nasync function updateBookmark(request, env, id, headers) {\n  try {\n    // 验证管理员权限\n    const authResult = await verifyAdmin(request, env);\n    if (!authResult.success) {\n      return new Response(JSON.stringify(authResult), {\n        status: 401,\n        headers\n      });\n    }\n\n    const updateData = await request.json();\n    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');\n    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];\n\n    const bookmarkIndex = bookmarks.findIndex(b => b.id === parseInt(id));\n    if (bookmarkIndex === -1) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Bookmark not found'\n      }), {\n        status: 404,\n        headers\n      });\n    }\n\n    // 更新书签数据\n    bookmarks[bookmarkIndex] = {\n      ...bookmarks[bookmarkIndex],\n      ...updateData,\n      id: parseInt(id), // 确保 ID 不被修改\n      updatedAt: new Date().toISOString()\n    };\n\n    await env.BOOKMARKS_KV.put('bookmarks', JSON.stringify(bookmarks));\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: bookmarks[bookmarkIndex]\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to update bookmark'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 删除书签\nasync function deleteBookmark(request, env, id, headers) {\n  try {\n    // 验证管理员权限\n    const authResult = await verifyAdmin(request, env);\n    if (!authResult.success) {\n      return new Response(JSON.stringify(authResult), {\n        status: 401,\n        headers\n      });\n    }\n\n    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');\n    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];\n\n    const bookmarkIndex = bookmarks.findIndex(b => b.id === parseInt(id));\n    if (bookmarkIndex === -1) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Bookmark not found'\n      }), {\n        status: 404,\n        headers\n      });\n    }\n\n    const deletedBookmark = bookmarks.splice(bookmarkIndex, 1)[0];\n    await env.BOOKMARKS_KV.put('bookmarks', JSON.stringify(bookmarks));\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: deletedBookmark\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to delete bookmark'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 处理留言 API 请求\nasync function handleMessagesAPI(request, env, corsHeaders) {\n  const url = new URL(request.url);\n  const method = request.method;\n  const pathParts = url.pathname.split('/');\n\n  const headers = {\n    ...corsHeaders,\n    'Content-Type': 'application/json'\n  };\n\n  // 检查是否有消息ID (用于更新和删除)\n  const messageId = pathParts[3]; // /api/messages/{id}\n\n  switch (method) {\n    case 'GET':\n      // 获取所有留言\n      return await getAllMessages(env, headers, url);\n\n    case 'POST':\n      // 添加新留言\n      return await addMessage(request, env, headers);\n\n    case 'PUT':\n      // 更新留言 (需要管理员权限)\n      if (!messageId) {\n        return new Response(JSON.stringify({ error: 'Message ID required' }), {\n          status: 400,\n          headers\n        });\n      }\n      return await updateMessage(request, env, headers, messageId);\n\n    case 'DELETE':\n      // 删除留言 (需要管理员权限)\n      if (!messageId) {\n        return new Response(JSON.stringify({ error: 'Message ID required' }), {\n          status: 400,\n          headers\n        });\n      }\n      return await deleteMessage(request, env, headers, messageId);\n\n    default:\n      return new Response(JSON.stringify({ error: 'Method not allowed' }), {\n        status: 405,\n        headers\n      });\n  }\n}\n\n// 获取所有留言（支持分页）\nasync function getAllMessages(env, headers, url) {\n  try {\n    const messagesData = await env.BOOKMARKS_KV.get('messages');\n    const allMessages = messagesData ? JSON.parse(messagesData) : [];\n\n    // 获取分页参数\n    const searchParams = new URLSearchParams(url.search);\n    const page = parseInt(searchParams.get('page')) || 1;\n    const limit = parseInt(searchParams.get('limit')) || 10;\n\n    // 计算分页\n    const total = allMessages.length;\n    const totalPages = Math.ceil(total / limit);\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n\n    // 按时间倒序排列（最新的在前面）\n    const sortedMessages = allMessages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n\n    // 获取当前页的数据\n    const messages = sortedMessages.slice(startIndex, endIndex);\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: {\n        messages,\n        pagination: {\n          page,\n          limit,\n          total,\n          totalPages,\n          hasNext: page < totalPages,\n          hasPrev: page > 1\n        }\n      }\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to fetch messages'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 添加新留言\nasync function addMessage(request, env, headers) {\n  try {\n    const messageData = await request.json();\n\n    // 验证输入数据\n    if (!messageData.name || !messageData.content) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Name and content are required'\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    // 限制长度\n    if (messageData.name.length > 20 || messageData.content.length > 500) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Name or content too long'\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    // 检查用户发言限制\n    const limitCheck = await checkUserLimits(request, env);\n    if (!limitCheck.allowed) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: limitCheck.error\n      }), {\n        status: 429, // Too Many Requests\n        headers\n      });\n    }\n\n    // 检查违禁词\n    const nameCheck = containsBannedWords(messageData.name);\n    const contentCheck = containsBannedWords(messageData.content);\n\n    if (nameCheck.hasBanned) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: `姓名包含不当内容，请修改后重试`\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    if (contentCheck.hasBanned) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: `留言内容包含不当内容，请修改后重试`\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    // 获取现有留言\n    const existingData = await env.BOOKMARKS_KV.get('messages');\n    const messages = existingData ? JSON.parse(existingData) : [];\n\n    // 生成唯一ID\n    const generateId = () => {\n      return Date.now().toString() + Math.random().toString(36).substring(2, 11);\n    };\n\n    // 创建新留言\n    const newMessage = {\n      id: generateId(),\n      name: messageData.name.trim(),\n      content: messageData.content.trim(),\n      createdAt: new Date().toISOString(),\n    };\n\n    messages.push(newMessage);\n\n    // 保存到 KV (只保留最新的 100 条留言)\n    const limitedMessages = messages.slice(-100);\n    await env.BOOKMARKS_KV.put('messages', JSON.stringify(limitedMessages));\n\n    // 记录用户发言（用于限制检查）\n    await recordUserMessage(request, env);\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: newMessage\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to add message'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 更新留言\nasync function updateMessage(request, env, headers, messageId) {\n  try {\n    // 验证管理员权限\n    const adminCheck = await verifyAdmin(request, env);\n    if (!adminCheck.success) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: adminCheck.error\n      }), {\n        status: 401,\n        headers\n      });\n    }\n\n    const messageData = await request.json();\n\n    // 验证输入数据\n    if (!messageData.name || !messageData.content) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Name and content are required'\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    // 限制长度\n    if (messageData.name.length > 20 || messageData.content.length > 500) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Name or content too long'\n      }), {\n        status: 400,\n        headers\n      });\n    }\n\n    // 获取现有留言\n    const existingData = await env.BOOKMARKS_KV.get('messages');\n    const messages = existingData ? JSON.parse(existingData) : [];\n\n    // 查找要更新的留言\n    const messageIndex = messages.findIndex(msg => msg.id === messageId);\n    if (messageIndex === -1) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Message not found'\n      }), {\n        status: 404,\n        headers\n      });\n    }\n\n    // 更新留言\n    const updatedMessage = {\n      ...messages[messageIndex],\n      name: messageData.name.trim(),\n      content: messageData.content.trim(),\n      updatedAt: new Date().toISOString()\n    };\n\n    messages[messageIndex] = updatedMessage;\n\n    // 保存到 KV\n    await env.BOOKMARKS_KV.put('messages', JSON.stringify(messages));\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: updatedMessage\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to update message'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 删除留言\nasync function deleteMessage(request, env, headers, messageId) {\n  try {\n    // 验证管理员权限\n    const adminCheck = await verifyAdmin(request, env);\n    if (!adminCheck.success) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: adminCheck.error\n      }), {\n        status: 401,\n        headers\n      });\n    }\n\n    // 获取现有留言\n    const existingData = await env.BOOKMARKS_KV.get('messages');\n    const messages = existingData ? JSON.parse(existingData) : [];\n\n    // 查找要删除的留言\n    const messageIndex = messages.findIndex(msg => msg.id === messageId);\n    if (messageIndex === -1) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Message not found'\n      }), {\n        status: 404,\n        headers\n      });\n    }\n\n    // 删除留言\n    const deletedMessage = messages[messageIndex];\n    messages.splice(messageIndex, 1);\n\n    // 保存到 KV\n    await env.BOOKMARKS_KV.put('messages', JSON.stringify(messages));\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: deletedMessage\n    }), { headers });\n  } catch (error) {\n    return new Response(JSON.stringify({\n      success: false,\n      error: 'Failed to delete message'\n    }), {\n      status: 500,\n      headers\n    });\n  }\n}\n\n// 违禁词库\nconst BANNED_WORDS = [\n  // 脏话和骂人的词\n  '傻逼', '傻B', 'SB', 'sb', '草泥马', '操你妈', '去死', '死全家', '滚蛋', '滚开',\n  '白痴', '智障', '脑残', '弱智', '垃圾', '废物', '混蛋', '王八蛋', '狗东西',\n  '妈的', 'TMD', 'tmd', '他妈的', '你妈', '你爸', '操', '艹', '靠', '卧槽',\n  '贱人', '婊子', '妓女', '鸡', '屌', '吊', '逼', 'B', '蛋', '屎', '尿',\n  // 政治敏感词\n  '法轮功', '六四', '天安门', '习近平', '毛泽东', '共产党', '民主', '自由',\n  // 其他不当内容\n  '黄色', '色情', '赌博', '毒品', '自杀', '杀人', '爆炸', '恐怖',\n  // 网络用语变体\n  'cnm', 'nmsl', 'wdnmd', 'rnm', 'qnm'\n];\n\n// 检查内容是否包含违禁词\nfunction containsBannedWords(text) {\n  const lowerText = text.toLowerCase();\n\n  for (const word of BANNED_WORDS) {\n    if (lowerText.includes(word.toLowerCase())) {\n      return {\n        hasBanned: true,\n        bannedWord: word\n      };\n    }\n  }\n\n  return { hasBanned: false };\n}\n\n// 获取客户端IP地址\nfunction getClientIP(request) {\n  // 尝试从不同的头部获取真实IP\n  const cfConnectingIP = request.headers.get('CF-Connecting-IP');\n  const xForwardedFor = request.headers.get('X-Forwarded-For');\n  const xRealIP = request.headers.get('X-Real-IP');\n\n  if (cfConnectingIP) return cfConnectingIP;\n  if (xForwardedFor) return xForwardedFor.split(',')[0].trim();\n  if (xRealIP) return xRealIP;\n\n  return 'unknown';\n}\n\n// 检查用户发言限制\nasync function checkUserLimits(request, env) {\n  const clientIP = getClientIP(request);\n  const now = new Date();\n  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);\n  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n\n  // 获取用户限制数据\n  const limitsKey = `user_limits_${clientIP}`;\n  const limitsData = await env.BOOKMARKS_KV.get(limitsKey);\n  const limits = limitsData ? JSON.parse(limitsData) : { messages: [] };\n\n  // 清理过期的记录（超过24小时的）\n  limits.messages = limits.messages.filter(timestamp => new Date(timestamp) > oneDayAgo);\n\n  // 检查1分钟内的限制\n  const recentMessages = limits.messages.filter(timestamp => new Date(timestamp) > oneMinuteAgo);\n  if (recentMessages.length >= 1) {\n    return {\n      allowed: false,\n      error: '发言过于频繁，请1分钟后再试'\n    };\n  }\n\n  // 检查24小时内的限制\n  if (limits.messages.length >= 5) {\n    return {\n      allowed: false,\n      error: '今日发言次数已达上限（5条），请明天再试'\n    };\n  }\n\n  return { allowed: true };\n}\n\n// 记录用户发言\nasync function recordUserMessage(request, env) {\n  const clientIP = getClientIP(request);\n  const now = new Date().toISOString();\n\n  const limitsKey = `user_limits_${clientIP}`;\n  const limitsData = await env.BOOKMARKS_KV.get(limitsKey);\n  const limits = limitsData ? JSON.parse(limitsData) : { messages: [] };\n\n  // 添加当前发言时间\n  limits.messages.push(now);\n\n  // 保存到KV（设置25小时过期，确保数据自动清理）\n  await env.BOOKMARKS_KV.put(limitsKey, JSON.stringify(limits), {\n    expirationTtl: 25 * 60 * 60 // 25小时\n  });\n}\n\n// 验证管理员权限\nasync function verifyAdmin(request, env) {\n  const authHeader = request.headers.get('Authorization');\n\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return { success: false, error: 'Missing or invalid authorization header' };\n  }\n\n  const token = authHeader.substring(7); // 移除 \"Bearer \" 前缀\n\n  if (token !== env.ADMIN_PASSWORD) {\n    return { success: false, error: 'Invalid admin password' };\n  }\n\n  return { success: true };\n}\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"Z:\\\\my\\\\vue-project - 普通版本\\\\src\\\\worker.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"Z:\\\\my\\\\vue-project - 普通版本\\\\src\\\\worker.js\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"Z:\\\\my\\\\vue-project - 普通版本\\\\.wrangler\\\\tmp\\\\bundle-NR7irB\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"Z:\\\\my\\\\vue-project - 普通版本\\\\.wrangler\\\\tmp\\\\bundle-NR7irB\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"Z:\\\\my\\\\vue-project - 普通版本\\\\.wrangler\\\\tmp\\\\bundle-NR7irB\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS,CAAC;AAAA;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC5BD,IAAO,iBAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,IAAI;AAGjB,UAAM,cAAc;AAAA,MAClB,+BAA+B;AAAA,MAC/B,gCAAgC;AAAA,MAChC,gCAAgC;AAAA,IAClC;AAGA,QAAI,QAAQ,WAAW,WAAW;AAChC,aAAO,IAAI,SAAS,MAAM,EAAE,SAAS,YAAY,CAAC;AAAA,IACpD;AAEA,QAAI;AAEF,UAAI,KAAK,WAAW,gBAAgB,GAAG;AACrC,eAAO,MAAM,mBAAmB,SAAS,KAAK,WAAW;AAAA,MAC3D;AAGA,UAAI,KAAK,WAAW,eAAe,GAAG;AACpC,eAAO,MAAM,kBAAkB,SAAS,KAAK,WAAW;AAAA,MAC1D;AAEA,aAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,KAAK,SAAS,YAAY,CAAC;AAAA,IACxE,SAAS,OAAO;AACd,cAAQ,MAAM,iBAAiB,KAAK;AACpC,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,wBAAwB,CAAC,GAAG;AAAA,QACtE,QAAQ;AAAA,QACR,SAAS,EAAE,GAAG,aAAa,gBAAgB,mBAAmB;AAAA,MAChE,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,eAAe,mBAAmB,SAAS,KAAK,aAAa;AAC3D,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,SAAS,QAAQ;AACvB,QAAM,YAAY,IAAI,SAAS,MAAM,GAAG;AAExC,QAAM,UAAU;AAAA,IACd,GAAG;AAAA,IACH,gBAAgB;AAAA,EAClB;AAEA,UAAQ,QAAQ;AAAA,IACd,KAAK;AAEH,UAAI,UAAU,CAAC,MAAM,QAAW;AAC9B,eAAO,MAAM,gBAAgB,KAAK,OAAO;AAAA,MAC3C,OAEK;AACH,cAAM,KAAK,UAAU,CAAC;AACtB,eAAO,MAAM,YAAY,KAAK,IAAI,OAAO;AAAA,MAC3C;AAAA,IAEF,KAAK;AAEH,aAAO,MAAM,YAAY,SAAS,KAAK,OAAO;AAAA,IAEhD,KAAK;AAEH,YAAM,WAAW,UAAU,CAAC;AAC5B,aAAO,MAAM,eAAe,SAAS,KAAK,UAAU,OAAO;AAAA,IAE7D,KAAK;AAEH,YAAM,WAAW,UAAU,CAAC;AAC5B,aAAO,MAAM,eAAe,SAAS,KAAK,UAAU,OAAO;AAAA,IAE7D;AACE,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,qBAAqB,CAAC,GAAG;AAAA,QACnE,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,EACL;AACF;AA1Ce;AA6Cf,eAAe,gBAAgB,KAAK,SAAS;AAC3C,MAAI;AACF,UAAM,gBAAgB,MAAM,IAAI,aAAa,IAAI,WAAW;AAC5D,UAAM,YAAY,gBAAgB,KAAK,MAAM,aAAa,IAAI,CAAC;AAE/D,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAlBe;AAqBf,eAAe,YAAY,KAAK,IAAI,SAAS;AAC3C,MAAI;AACF,UAAM,gBAAgB,MAAM,IAAI,aAAa,IAAI,WAAW;AAC5D,UAAM,YAAY,gBAAgB,KAAK,MAAM,aAAa,IAAI,CAAC;AAC/D,UAAM,WAAW,UAAU,KAAK,OAAK,EAAE,OAAO,SAAS,EAAE,CAAC;AAE1D,QAAI,CAAC,UAAU;AACb,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AA7Be;AAgCf,eAAe,YAAY,SAAS,KAAK,SAAS;AAChD,MAAI;AAEF,UAAM,aAAa,MAAM,YAAY,SAAS,GAAG;AACjD,QAAI,CAAC,WAAW,SAAS;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU,UAAU,GAAG;AAAA,QAC9C,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,cAAc,MAAM,QAAQ,KAAK;AAGvC,QAAI,CAAC,YAAY,QAAQ,CAAC,YAAY,OAAO,CAAC,YAAY,aAAa;AACrE,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM,IAAI,aAAa,IAAI,WAAW;AAC5D,UAAM,YAAY,gBAAgB,KAAK,MAAM,aAAa,IAAI,CAAC;AAG/D,UAAM,QAAQ,UAAU,SAAS,IAAI,KAAK,IAAI,GAAG,UAAU,IAAI,OAAK,EAAE,EAAE,CAAC,IAAI,IAAI;AAEjF,UAAM,WAAW;AAAA,MACf,IAAI;AAAA,MACJ,MAAM,YAAY;AAAA,MAClB,aAAa,YAAY;AAAA,MACzB,KAAK,YAAY;AAAA,MACjB,MAAM,YAAY,QAAQ;AAAA,MAC1B,UAAU,YAAY,YAAY;AAAA,MAClC,OAAO,YAAY,SAAS;AAAA,MAC5B,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,cAAU,KAAK,QAAQ;AACvB,UAAM,IAAI,aAAa,IAAI,aAAa,KAAK,UAAU,SAAS,CAAC;AAEjE,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAzDe;AA4Df,eAAe,eAAe,SAAS,KAAK,IAAI,SAAS;AACvD,MAAI;AAEF,UAAM,aAAa,MAAM,YAAY,SAAS,GAAG;AACjD,QAAI,CAAC,WAAW,SAAS;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU,UAAU,GAAG;AAAA,QAC9C,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,aAAa,MAAM,QAAQ,KAAK;AACtC,UAAM,gBAAgB,MAAM,IAAI,aAAa,IAAI,WAAW;AAC5D,UAAM,YAAY,gBAAgB,KAAK,MAAM,aAAa,IAAI,CAAC;AAE/D,UAAM,gBAAgB,UAAU,UAAU,OAAK,EAAE,OAAO,SAAS,EAAE,CAAC;AACpE,QAAI,kBAAkB,IAAI;AACxB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,cAAU,aAAa,IAAI;AAAA,MACzB,GAAG,UAAU,aAAa;AAAA,MAC1B,GAAG;AAAA,MACH,IAAI,SAAS,EAAE;AAAA;AAAA,MACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,UAAM,IAAI,aAAa,IAAI,aAAa,KAAK,UAAU,SAAS,CAAC;AAEjE,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM,UAAU,aAAa;AAAA,IAC/B,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAjDe;AAoDf,eAAe,eAAe,SAAS,KAAK,IAAI,SAAS;AACvD,MAAI;AAEF,UAAM,aAAa,MAAM,YAAY,SAAS,GAAG;AACjD,QAAI,CAAC,WAAW,SAAS;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU,UAAU,GAAG;AAAA,QAC9C,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM,IAAI,aAAa,IAAI,WAAW;AAC5D,UAAM,YAAY,gBAAgB,KAAK,MAAM,aAAa,IAAI,CAAC;AAE/D,UAAM,gBAAgB,UAAU,UAAU,OAAK,EAAE,OAAO,SAAS,EAAE,CAAC;AACpE,QAAI,kBAAkB,IAAI;AACxB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,kBAAkB,UAAU,OAAO,eAAe,CAAC,EAAE,CAAC;AAC5D,UAAM,IAAI,aAAa,IAAI,aAAa,KAAK,UAAU,SAAS,CAAC;AAEjE,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAzCe;AA4Cf,eAAe,kBAAkB,SAAS,KAAK,aAAa;AAC1D,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,SAAS,QAAQ;AACvB,QAAM,YAAY,IAAI,SAAS,MAAM,GAAG;AAExC,QAAM,UAAU;AAAA,IACd,GAAG;AAAA,IACH,gBAAgB;AAAA,EAClB;AAGA,QAAM,YAAY,UAAU,CAAC;AAE7B,UAAQ,QAAQ;AAAA,IACd,KAAK;AAEH,aAAO,MAAM,eAAe,KAAK,SAAS,GAAG;AAAA,IAE/C,KAAK;AAEH,aAAO,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IAE/C,KAAK;AAEH,UAAI,CAAC,WAAW;AACd,eAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,sBAAsB,CAAC,GAAG;AAAA,UACpE,QAAQ;AAAA,UACR;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,MAAM,cAAc,SAAS,KAAK,SAAS,SAAS;AAAA,IAE7D,KAAK;AAEH,UAAI,CAAC,WAAW;AACd,eAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,sBAAsB,CAAC,GAAG;AAAA,UACpE,QAAQ;AAAA,UACR;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,MAAM,cAAc,SAAS,KAAK,SAAS,SAAS;AAAA,IAE7D;AACE,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,OAAO,qBAAqB,CAAC,GAAG;AAAA,QACnE,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,EACL;AACF;AAhDe;AAmDf,eAAe,eAAe,KAAK,SAAS,KAAK;AAC/C,MAAI;AACF,UAAM,eAAe,MAAM,IAAI,aAAa,IAAI,UAAU;AAC1D,UAAM,cAAc,eAAe,KAAK,MAAM,YAAY,IAAI,CAAC;AAG/D,UAAM,eAAe,IAAI,gBAAgB,IAAI,MAAM;AACnD,UAAM,OAAO,SAAS,aAAa,IAAI,MAAM,CAAC,KAAK;AACnD,UAAM,QAAQ,SAAS,aAAa,IAAI,OAAO,CAAC,KAAK;AAGrD,UAAM,QAAQ,YAAY;AAC1B,UAAM,aAAa,KAAK,KAAK,QAAQ,KAAK;AAC1C,UAAM,cAAc,OAAO,KAAK;AAChC,UAAM,WAAW,aAAa;AAG9B,UAAM,iBAAiB,YAAY,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AAG/F,UAAM,WAAW,eAAe,MAAM,YAAY,QAAQ;AAE1D,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,QACJ;AAAA,QACA,YAAY;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,OAAO;AAAA,UAChB,SAAS,OAAO;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AA7Ce;AAgDf,eAAe,WAAW,SAAS,KAAK,SAAS;AAC/C,MAAI;AACF,UAAM,cAAc,MAAM,QAAQ,KAAK;AAGvC,QAAI,CAAC,YAAY,QAAQ,CAAC,YAAY,SAAS;AAC7C,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,YAAY,KAAK,SAAS,MAAM,YAAY,QAAQ,SAAS,KAAK;AACpE,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,aAAa,MAAM,gBAAgB,SAAS,GAAG;AACrD,QAAI,CAAC,WAAW,SAAS;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO,WAAW;AAAA,MACpB,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,YAAY,oBAAoB,YAAY,IAAI;AACtD,UAAM,eAAe,oBAAoB,YAAY,OAAO;AAE5D,QAAI,UAAU,WAAW;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,aAAa,WAAW;AAC1B,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,eAAe,MAAM,IAAI,aAAa,IAAI,UAAU;AAC1D,UAAM,WAAW,eAAe,KAAK,MAAM,YAAY,IAAI,CAAC;AAG5D,UAAM,aAAa,6BAAM;AACvB,aAAO,KAAK,IAAI,EAAE,SAAS,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,IAC3E,GAFmB;AAKnB,UAAM,aAAa;AAAA,MACjB,IAAI,WAAW;AAAA,MACf,MAAM,YAAY,KAAK,KAAK;AAAA,MAC5B,SAAS,YAAY,QAAQ,KAAK;AAAA,MAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,aAAS,KAAK,UAAU;AAGxB,UAAM,kBAAkB,SAAS,MAAM,IAAI;AAC3C,UAAM,IAAI,aAAa,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;AAGtE,UAAM,kBAAkB,SAAS,GAAG;AAEpC,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AArGe;AAwGf,eAAe,cAAc,SAAS,KAAK,SAAS,WAAW;AAC7D,MAAI;AAEF,UAAM,aAAa,MAAM,YAAY,SAAS,GAAG;AACjD,QAAI,CAAC,WAAW,SAAS;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO,WAAW;AAAA,MACpB,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,cAAc,MAAM,QAAQ,KAAK;AAGvC,QAAI,CAAC,YAAY,QAAQ,CAAC,YAAY,SAAS;AAC7C,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,YAAY,KAAK,SAAS,MAAM,YAAY,QAAQ,SAAS,KAAK;AACpE,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,eAAe,MAAM,IAAI,aAAa,IAAI,UAAU;AAC1D,UAAM,WAAW,eAAe,KAAK,MAAM,YAAY,IAAI,CAAC;AAG5D,UAAM,eAAe,SAAS,UAAU,SAAO,IAAI,OAAO,SAAS;AACnE,QAAI,iBAAiB,IAAI;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,iBAAiB;AAAA,MACrB,GAAG,SAAS,YAAY;AAAA,MACxB,MAAM,YAAY,KAAK,KAAK;AAAA,MAC5B,SAAS,YAAY,QAAQ,KAAK;AAAA,MAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,aAAS,YAAY,IAAI;AAGzB,UAAM,IAAI,aAAa,IAAI,YAAY,KAAK,UAAU,QAAQ,CAAC;AAE/D,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAhFe;AAmFf,eAAe,cAAc,SAAS,KAAK,SAAS,WAAW;AAC7D,MAAI;AAEF,UAAM,aAAa,MAAM,YAAY,SAAS,GAAG;AACjD,QAAI,CAAC,WAAW,SAAS;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO,WAAW;AAAA,MACpB,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,eAAe,MAAM,IAAI,aAAa,IAAI,UAAU;AAC1D,UAAM,WAAW,eAAe,KAAK,MAAM,YAAY,IAAI,CAAC;AAG5D,UAAM,eAAe,SAAS,UAAU,SAAO,IAAI,OAAO,SAAS;AACnE,QAAI,iBAAiB,IAAI;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,iBAAiB,SAAS,YAAY;AAC5C,aAAS,OAAO,cAAc,CAAC;AAG/B,UAAM,IAAI,aAAa,IAAI,YAAY,KAAK,UAAU,QAAQ,CAAC;AAE/D,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,EACjB,SAAS,OAAO;AACd,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAlDe;AAqDf,IAAM,eAAe;AAAA;AAAA,EAEnB;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAO;AAAA,EAAM;AAAA,EACzD;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EACjD;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EACtD;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA;AAAA,EAErD;AAAA,EAAO;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA;AAAA,EAE/C;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA;AAAA,EAE1C;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AACjC;AAGA,SAAS,oBAAoB,MAAM;AACjC,QAAM,YAAY,KAAK,YAAY;AAEnC,aAAW,QAAQ,cAAc;AAC/B,QAAI,UAAU,SAAS,KAAK,YAAY,CAAC,GAAG;AAC1C,aAAO;AAAA,QACL,WAAW;AAAA,QACX,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,WAAW,MAAM;AAC5B;AAbS;AAgBT,SAAS,YAAY,SAAS;AAE5B,QAAM,iBAAiB,QAAQ,QAAQ,IAAI,kBAAkB;AAC7D,QAAM,gBAAgB,QAAQ,QAAQ,IAAI,iBAAiB;AAC3D,QAAM,UAAU,QAAQ,QAAQ,IAAI,WAAW;AAE/C,MAAI,eAAgB,QAAO;AAC3B,MAAI,cAAe,QAAO,cAAc,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK;AAC3D,MAAI,QAAS,QAAO;AAEpB,SAAO;AACT;AAXS;AAcT,eAAe,gBAAgB,SAAS,KAAK;AAC3C,QAAM,WAAW,YAAY,OAAO;AACpC,QAAM,MAAM,oBAAI,KAAK;AACrB,QAAM,eAAe,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,GAAI;AACvD,QAAM,YAAY,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,KAAK,GAAI;AAG9D,QAAM,YAAY,eAAe,QAAQ;AACzC,QAAM,aAAa,MAAM,IAAI,aAAa,IAAI,SAAS;AACvD,QAAM,SAAS,aAAa,KAAK,MAAM,UAAU,IAAI,EAAE,UAAU,CAAC,EAAE;AAGpE,SAAO,WAAW,OAAO,SAAS,OAAO,eAAa,IAAI,KAAK,SAAS,IAAI,SAAS;AAGrF,QAAM,iBAAiB,OAAO,SAAS,OAAO,eAAa,IAAI,KAAK,SAAS,IAAI,YAAY;AAC7F,MAAI,eAAe,UAAU,GAAG;AAC9B,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,OAAO,SAAS,UAAU,GAAG;AAC/B,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,EAAE,SAAS,KAAK;AACzB;AAhCe;AAmCf,eAAe,kBAAkB,SAAS,KAAK;AAC7C,QAAM,WAAW,YAAY,OAAO;AACpC,QAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAEnC,QAAM,YAAY,eAAe,QAAQ;AACzC,QAAM,aAAa,MAAM,IAAI,aAAa,IAAI,SAAS;AACvD,QAAM,SAAS,aAAa,KAAK,MAAM,UAAU,IAAI,EAAE,UAAU,CAAC,EAAE;AAGpE,SAAO,SAAS,KAAK,GAAG;AAGxB,QAAM,IAAI,aAAa,IAAI,WAAW,KAAK,UAAU,MAAM,GAAG;AAAA,IAC5D,eAAe,KAAK,KAAK;AAAA;AAAA,EAC3B,CAAC;AACH;AAfe;AAkBf,eAAe,YAAY,SAAS,KAAK;AACvC,QAAM,aAAa,QAAQ,QAAQ,IAAI,eAAe;AAEtD,MAAI,CAAC,cAAc,CAAC,WAAW,WAAW,SAAS,GAAG;AACpD,WAAO,EAAE,SAAS,OAAO,OAAO,0CAA0C;AAAA,EAC5E;AAEA,QAAM,QAAQ,WAAW,UAAU,CAAC;AAEpC,MAAI,UAAU,IAAI,gBAAgB;AAChC,WAAO,EAAE,SAAS,OAAO,OAAO,yBAAyB;AAAA,EAC3D;AAEA,SAAO,EAAE,SAAS,KAAK;AACzB;AAde;;;AC1tBf,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}