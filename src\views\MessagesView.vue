<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  messageService,
  type Message,
  type MessageInput,
  type MessagePagination,
} from "../services/bookmarkService";

const messages = ref<Message[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);
const submitting = ref(false);

// 分页数据
const pagination = ref<MessagePagination>({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrev: false,
});

// 表单数据
const form = ref<MessageInput>({
  name: "",
  content: "",
});

// 表单验证
const isFormValid = computed(() => {
  return form.value.name.trim().length > 0 && form.value.content.trim().length > 0;
});

// 加载留言数据
const loadMessages = async (page: number = pagination.value.page) => {
  try {
    loading.value = true;
    error.value = null;

    const response = await messageService.getAllMessages(page, pagination.value.limit);

    if (response.success && response.data) {
      messages.value = response.data.messages;
      pagination.value = response.data.pagination;
    } else {
      error.value = response.error || "加载留言失败";
      // 如果 API 失败，使用空数组
      messages.value = [];
    }
  } catch (err) {
    console.error("Failed to load messages:", err);
    error.value = "网络连接失败";
    messages.value = [];
  } finally {
    loading.value = false;
  }
};

// 分页操作
const goToPage = (page: number) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.page = page;
    loadMessages(page);
  }
};

const nextPage = () => {
  if (pagination.value.hasNext) {
    goToPage(pagination.value.page + 1);
  }
};

const prevPage = () => {
  if (pagination.value.hasPrev) {
    goToPage(pagination.value.page - 1);
  }
};

// 获取页码数组（显示当前页前后各2页）
const getPageNumbers = () => {
  const current = pagination.value.page;
  const total = pagination.value.totalPages;
  const pages: number[] = [];

  // 计算显示范围
  let start = Math.max(1, current - 2);
  let end = Math.min(total, current + 2);

  // 确保显示5个页码（如果总页数足够）
  if (end - start < 4) {
    if (start === 1) {
      end = Math.min(total, start + 4);
    } else {
      start = Math.max(1, end - 4);
    }
  }

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
};

// 提交留言
const submitMessage = async () => {
  if (!isFormValid.value || submitting.value) return;

  try {
    submitting.value = true;

    const response = await messageService.addMessage({
      name: form.value.name.trim(),
      content: form.value.content.trim(),
    });

    if (response.success && response.data) {
      // 添加成功，重新加载第一页留言列表
      pagination.value.page = 1;
      await loadMessages(1);
      // 清空表单
      form.value = { name: "", content: "" };
      // 显示成功提示
      showSuccessMessage();
    } else {
      error.value = response.error || "提交留言失败";
    }
  } catch (err) {
    console.error("Failed to submit message:", err);
    error.value = "网络连接失败";
  } finally {
    submitting.value = false;
  }
};

// 显示成功提示
const showSuccessMessage = () => {
  const successEl = document.querySelector(".success-message");
  if (successEl) {
    successEl.classList.add("show");
    setTimeout(() => {
      successEl.classList.remove("show");
    }, 3000);
  }
};

// 格式化时间
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return "刚刚";
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 30) return `${days}天前`;

  return date.toLocaleDateString("zh-CN");
};

// 重新加载数据
const refreshMessages = () => {
  loadMessages();
};

// 组件挂载时加载数据
onMounted(() => {
  loadMessages();
});
</script>

<template>
  <div class="messages-view">
    <div class="container">
      <!-- 页面标题 -->
      <div class="messages-header">
        <h1 class="page-title">留言板</h1>
        <p class="page-subtitle">分享你的想法，留下美好的回忆</p>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 留言列表（左侧） -->
        <div class="messages-section">
          <div class="section-header">
            <h2 class="section-title">所有留言</h2>
            <button @click="refreshMessages" class="refresh-btn" :disabled="loading">
              <svg
                class="refresh-icon"
                :class="{ spinning: loading }"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path
                  d="m20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"
                ></path>
              </svg>
              刷新
            </button>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载留言...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button @click="refreshMessages" class="retry-btn">重试</button>
          </div>

          <!-- 留言列表 -->
          <div v-else-if="messages.length > 0">
            <div class="messages-list">
              <div v-for="message in messages" :key="message.id" class="message-card">
                <div class="message-header">
                  <div class="message-author">
                    <div class="author-avatar">{{ message.name.charAt(0).toUpperCase() }}</div>
                    <div class="author-info">
                      <h4 class="author-name">{{ message.name }}</h4>
                      <span class="message-time">{{ formatDate(message.createdAt) }}</span>
                    </div>
                  </div>
                </div>
                <div class="message-content">
                  <p>{{ message.content }}</p>
                </div>
              </div>
            </div>

            <!-- 分页组件 -->
            <div v-if="pagination.totalPages > 1" class="pagination">
              <div class="pagination-info">
                <span
                  >共 {{ pagination.total }} 条留言，第 {{ pagination.page }} /
                  {{ pagination.totalPages }} 页</span
                >
              </div>
              <div class="pagination-controls">
                <button
                  @click="prevPage"
                  :disabled="!pagination.hasPrev"
                  class="pagination-btn"
                  :class="{ disabled: !pagination.hasPrev }"
                >
                  ← 上一页
                </button>

                <div class="page-numbers">
                  <button
                    v-for="page in getPageNumbers()"
                    :key="page"
                    @click="goToPage(page)"
                    class="page-btn"
                    :class="{ active: page === pagination.page }"
                  >
                    {{ page }}
                  </button>
                </div>

                <button
                  @click="nextPage"
                  :disabled="!pagination.hasNext"
                  class="pagination-btn"
                  :class="{ disabled: !pagination.hasNext }"
                >
                  下一页 →
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">💭</div>
            <h3>还没有留言</h3>
            <p>成为第一个留言的人吧！</p>
          </div>
        </div>

        <!-- 留言表单（右侧） -->
        <div class="message-form-section">
          <div class="form-card">
            <h2 class="form-title">写下你的留言</h2>
            <form @submit.prevent="submitMessage" class="message-form">
              <div class="form-group">
                <label for="name" class="form-label">姓名</label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  placeholder="请输入你的姓名"
                  class="form-input"
                  maxlength="20"
                  required
                />
              </div>

              <div class="form-group">
                <label for="content" class="form-label">留言内容</label>
                <textarea
                  id="content"
                  v-model="form.content"
                  placeholder="分享你的想法..."
                  class="form-textarea"
                  rows="4"
                  maxlength="500"
                  required
                ></textarea>
                <div class="char-count">{{ form.content.length }}/500</div>
              </div>

              <button type="submit" :disabled="!isFormValid || submitting" class="submit-btn">
                <span v-if="submitting">提交中...</span>
                <span v-else>发送留言</span>
              </button>
            </form>

            <!-- 错误提示 -->
            <div v-if="error" class="error-message">
              {{ error }}
            </div>

            <!-- 成功提示 -->
            <div v-if="showSuccess" class="success-message">
              <div class="success-content">
                <span class="success-icon">✅</span>
                <span>留言提交成功！</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.messages-view {
  min-height: 100vh;
  padding-top: 8rem;
  padding-bottom: 4rem;
  background: linear-gradient(135deg, var(--background-color) 0%, #0f0f1a 100%);
}

.messages-header {
  text-align: center;
  margin-bottom: 4rem;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.2rem;
  color: rgba(232, 232, 240, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 主要内容区域 - PC端左右布局 */
.main-content {
  display: flex;
  gap: 3rem;
  align-items: flex-start;
}

/* 留言列表区域 - 左侧，占更多空间 */
.messages-section {
  flex: 2;
  min-width: 0; /* 防止flex项目溢出 */
}

/* 留言表单区域 - 右侧，固定宽度 */
.message-form-section {
  flex: 1;
  max-width: 400px;
  min-width: 350px;
}

.form-card {
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  width: 100%;
  backdrop-filter: blur(10px);
  position: sticky;
  top: 2rem; /* 滚动时固定在顶部 */
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2rem;
  text-align: center;
}

.message-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-color);
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  resize: vertical;
}

.form-input:focus,
.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgba(232, 232, 240, 0.5);
}

.char-count {
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  font-size: 0.8rem;
  color: rgba(232, 232, 240, 0.5);
}

.submit-btn {
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  color: var(--background-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.success-message {
  position: fixed;
  top: 100px;
  right: 20px;
  background: linear-gradient(90deg, #10b981, #059669);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
  transform: translateX(400px);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1000;
}

.success-message.show {
  transform: translateX(0);
  opacity: 1;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-icon {
  font-size: 1.2rem;
}

/* 留言列表区域样式已在上面定义 */

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 0.8rem 1.5rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-icon {
  transition: transform 0.3s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message-card {
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.message-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 212, 255, 0.3);
}

.message-header {
  margin-bottom: 1rem;
}

.message-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--background-color);
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.message-time {
  font-size: 0.8rem;
  color: rgba(232, 232, 240, 0.6);
}

.message-content {
  color: rgba(232, 232, 240, 0.8);
  line-height: 1.6;
}

.message-content p {
  margin: 0;
  word-wrap: break-word;
}

/* 加载、错误、空状态样式 */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(232, 232, 240, 0.7);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.3);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.error-icon,
.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-state h3,
.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.retry-btn {
  background: var(--primary-color);
  color: var(--background-color);
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.retry-btn:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

@media (max-width: 768px) {
  .messages-view {
    padding-top: 6rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .form-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .message-card {
    padding: 1.2rem;
  }

  .author-avatar {
    width: 35px;
    height: 35px;
  }
}

/* 分页样式 */
.pagination {
  margin-top: 3rem;
  padding: 2rem;
  background: rgba(26, 26, 46, 0.6);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
  text-align: center;
  margin-bottom: 1.5rem;
  color: rgba(232, 232, 240, 0.7);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.pagination-btn:hover:not(.disabled) {
  background: rgba(0, 212, 255, 0.2);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: rgba(232, 232, 240, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.page-numbers {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.page-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(26, 26, 46, 0.8);
  border-radius: 8px;
  color: rgba(232, 232, 240, 0.8);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.page-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--background-color);
  font-weight: 600;
}

/* 手机端响应式样式 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 2rem;
  }

  .messages-section {
    order: 2; /* 手机端留言列表在下方 */
  }

  .message-form-section {
    order: 1; /* 手机端表单在上方 */
    max-width: none;
    min-width: auto;
  }

  .form-card {
    position: static; /* 手机端不固定 */
    top: auto;
  }
}

@media (max-width: 768px) {
  .messages-view {
    padding-top: 6rem;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .form-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .message-card {
    padding: 1.2rem;
  }

  .author-avatar {
    width: 35px;
    height: 35px;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .page-numbers {
    order: -1;
  }

  .pagination-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}
</style>
