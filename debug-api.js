// API调试脚本
const API_BASE_URL = 'https://www.jisoolove.top';
const ADMIN_PASSWORD = 'jisoo521';

async function testAPI() {
  console.log('🧪 开始测试 API...\n');
  console.log('🔗 API Base URL:', API_BASE_URL);
  console.log('🔑 Admin Password:', ADMIN_PASSWORD);
  console.log('\n');

  // 测试1: 获取书签列表（不需要认证）
  console.log('📋 测试1: 获取书签列表');
  try {
    console.log('📡 发送请求到:', `${API_BASE_URL}/api/bookmarks`);
    const response = await fetch(`${API_BASE_URL}/api/bookmarks`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    console.log('📡 响应状态:', response.status, response.statusText);
    const result = await response.json();
    console.log('✅ 获取书签成功:', result.success);
    console.log('📊 书签数量:', result.data?.length || 0);
  } catch (error) {
    console.log('❌ 获取书签失败:', error.message);
    console.log('❌ 错误详情:', error);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试2: 验证管理员密码
  console.log('🔐 测试2: 验证管理员密码');
  try {
    const response = await fetch(`${API_BASE_URL}/api/bookmarks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ADMIN_PASSWORD}`
      },
      body: JSON.stringify({
        name: 'test-bookmark',
        description: 'test description',
        url: 'https://test.com',
        icon: '🔗',
        category: '测试',
        color: '#00d4ff'
      })
    });

    console.log('📡 响应状态:', response.status);
    console.log('📡 响应状态文本:', response.statusText);

    const result = await response.json();
    console.log('📄 响应内容:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ 密码验证成功！');
      // 删除测试书签
      if (result.data?.id) {
        const deleteResponse = await fetch(`${API_BASE_URL}/api/bookmarks/${result.data.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${ADMIN_PASSWORD}`
          }
        });
        const deleteResult = await deleteResponse.json();
        console.log('🗑️ 删除测试书签:', deleteResult.success ? '成功' : '失败');
      }
    } else {
      console.log('❌ 密码验证失败:', result.error);
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试3: 检查CORS
  console.log('🌐 测试3: 检查CORS');
  try {
    const response = await fetch(`${API_BASE_URL}/api/bookmarks`, {
      method: 'OPTIONS'
    });
    console.log('📡 OPTIONS响应状态:', response.status);
    console.log('🔗 CORS头信息:');
    console.log('  - Access-Control-Allow-Origin:', response.headers.get('Access-Control-Allow-Origin'));
    console.log('  - Access-Control-Allow-Methods:', response.headers.get('Access-Control-Allow-Methods'));
    console.log('  - Access-Control-Allow-Headers:', response.headers.get('Access-Control-Allow-Headers'));
  } catch (error) {
    console.log('❌ CORS检查失败:', error.message);
  }
}

// 运行测试
testAPI().catch(console.error);
