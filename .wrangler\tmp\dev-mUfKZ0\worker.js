var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-NR7irB/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// src/worker.js
var worker_default = {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization"
    };
    if (request.method === "OPTIONS") {
      return new Response(null, { headers: corsHeaders });
    }
    try {
      if (path.startsWith("/api/bookmarks")) {
        return await handleBookmarksAPI(request, env, corsHeaders);
      }
      if (path.startsWith("/api/messages")) {
        return await handleMessagesAPI(request, env, corsHeaders);
      }
      return new Response("Not Found", { status: 404, headers: corsHeaders });
    } catch (error) {
      console.error("Worker error:", error);
      return new Response(JSON.stringify({ error: "Internal Server Error" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      });
    }
  }
};
async function handleBookmarksAPI(request, env, corsHeaders) {
  const url = new URL(request.url);
  const method = request.method;
  const pathParts = url.pathname.split("/");
  const headers = {
    ...corsHeaders,
    "Content-Type": "application/json"
  };
  switch (method) {
    case "GET":
      if (pathParts[3] === void 0) {
        return await getAllBookmarks(env, headers);
      } else {
        const id = pathParts[3];
        return await getBookmark(env, id, headers);
      }
    case "POST":
      return await addBookmark(request, env, headers);
    case "PUT":
      const updateId = pathParts[3];
      return await updateBookmark(request, env, updateId, headers);
    case "DELETE":
      const deleteId = pathParts[3];
      return await deleteBookmark(request, env, deleteId, headers);
    default:
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers
      });
  }
}
__name(handleBookmarksAPI, "handleBookmarksAPI");
async function getAllBookmarks(env, headers) {
  try {
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    return new Response(JSON.stringify({
      success: true,
      data: bookmarks
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch bookmarks"
    }), {
      status: 500,
      headers
    });
  }
}
__name(getAllBookmarks, "getAllBookmarks");
async function getBookmark(env, id, headers) {
  try {
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmark = bookmarks.find((b) => b.id === parseInt(id));
    if (!bookmark) {
      return new Response(JSON.stringify({
        success: false,
        error: "Bookmark not found"
      }), {
        status: 404,
        headers
      });
    }
    return new Response(JSON.stringify({
      success: true,
      data: bookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(getBookmark, "getBookmark");
async function addBookmark(request, env, headers) {
  try {
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }
    const newBookmark = await request.json();
    if (!newBookmark.name || !newBookmark.url || !newBookmark.description) {
      return new Response(JSON.stringify({
        success: false,
        error: "Missing required fields: name, url, description"
      }), {
        status: 400,
        headers
      });
    }
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const newId = bookmarks.length > 0 ? Math.max(...bookmarks.map((b) => b.id)) + 1 : 1;
    const bookmark = {
      id: newId,
      name: newBookmark.name,
      description: newBookmark.description,
      url: newBookmark.url,
      icon: newBookmark.icon || "\u{1F517}",
      category: newBookmark.category || "\u5176\u4ED6",
      color: newBookmark.color || "#00d4ff",
      createdAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    bookmarks.push(bookmark);
    await env.BOOKMARKS_KV.put("bookmarks", JSON.stringify(bookmarks));
    return new Response(JSON.stringify({
      success: true,
      data: bookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to add bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(addBookmark, "addBookmark");
async function updateBookmark(request, env, id, headers) {
  try {
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }
    const updateData = await request.json();
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmarkIndex = bookmarks.findIndex((b) => b.id === parseInt(id));
    if (bookmarkIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Bookmark not found"
      }), {
        status: 404,
        headers
      });
    }
    bookmarks[bookmarkIndex] = {
      ...bookmarks[bookmarkIndex],
      ...updateData,
      id: parseInt(id),
      // 确保 ID 不被修改
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    await env.BOOKMARKS_KV.put("bookmarks", JSON.stringify(bookmarks));
    return new Response(JSON.stringify({
      success: true,
      data: bookmarks[bookmarkIndex]
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to update bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(updateBookmark, "updateBookmark");
async function deleteBookmark(request, env, id, headers) {
  try {
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmarkIndex = bookmarks.findIndex((b) => b.id === parseInt(id));
    if (bookmarkIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Bookmark not found"
      }), {
        status: 404,
        headers
      });
    }
    const deletedBookmark = bookmarks.splice(bookmarkIndex, 1)[0];
    await env.BOOKMARKS_KV.put("bookmarks", JSON.stringify(bookmarks));
    return new Response(JSON.stringify({
      success: true,
      data: deletedBookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to delete bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(deleteBookmark, "deleteBookmark");
async function handleMessagesAPI(request, env, corsHeaders) {
  const url = new URL(request.url);
  const method = request.method;
  const pathParts = url.pathname.split("/");
  const headers = {
    ...corsHeaders,
    "Content-Type": "application/json"
  };
  const messageId = pathParts[3];
  switch (method) {
    case "GET":
      return await getAllMessages(env, headers, url);
    case "POST":
      return await addMessage(request, env, headers);
    case "PUT":
      if (!messageId) {
        return new Response(JSON.stringify({ error: "Message ID required" }), {
          status: 400,
          headers
        });
      }
      return await updateMessage(request, env, headers, messageId);
    case "DELETE":
      if (!messageId) {
        return new Response(JSON.stringify({ error: "Message ID required" }), {
          status: 400,
          headers
        });
      }
      return await deleteMessage(request, env, headers, messageId);
    default:
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers
      });
  }
}
__name(handleMessagesAPI, "handleMessagesAPI");
async function getAllMessages(env, headers, url) {
  try {
    const messagesData = await env.BOOKMARKS_KV.get("messages");
    const allMessages = messagesData ? JSON.parse(messagesData) : [];
    const searchParams = new URLSearchParams(url.search);
    const page = parseInt(searchParams.get("page")) || 1;
    const limit = parseInt(searchParams.get("limit")) || 10;
    const total = allMessages.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const sortedMessages = allMessages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    const messages = sortedMessages.slice(startIndex, endIndex);
    return new Response(JSON.stringify({
      success: true,
      data: {
        messages,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch messages"
    }), {
      status: 500,
      headers
    });
  }
}
__name(getAllMessages, "getAllMessages");
async function addMessage(request, env, headers) {
  try {
    const messageData = await request.json();
    if (!messageData.name || !messageData.content) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name and content are required"
      }), {
        status: 400,
        headers
      });
    }
    if (messageData.name.length > 20 || messageData.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name or content too long"
      }), {
        status: 400,
        headers
      });
    }
    const limitCheck = await checkUserLimits(request, env);
    if (!limitCheck.allowed) {
      return new Response(JSON.stringify({
        success: false,
        error: limitCheck.error
      }), {
        status: 429,
        // Too Many Requests
        headers
      });
    }
    const nameCheck = containsBannedWords(messageData.name);
    const contentCheck = containsBannedWords(messageData.content);
    if (nameCheck.hasBanned) {
      return new Response(JSON.stringify({
        success: false,
        error: `\u59D3\u540D\u5305\u542B\u4E0D\u5F53\u5185\u5BB9\uFF0C\u8BF7\u4FEE\u6539\u540E\u91CD\u8BD5`
      }), {
        status: 400,
        headers
      });
    }
    if (contentCheck.hasBanned) {
      return new Response(JSON.stringify({
        success: false,
        error: `\u7559\u8A00\u5185\u5BB9\u5305\u542B\u4E0D\u5F53\u5185\u5BB9\uFF0C\u8BF7\u4FEE\u6539\u540E\u91CD\u8BD5`
      }), {
        status: 400,
        headers
      });
    }
    const existingData = await env.BOOKMARKS_KV.get("messages");
    const messages = existingData ? JSON.parse(existingData) : [];
    const generateId = /* @__PURE__ */ __name(() => {
      return Date.now().toString() + Math.random().toString(36).substring(2, 11);
    }, "generateId");
    const newMessage = {
      id: generateId(),
      name: messageData.name.trim(),
      content: messageData.content.trim(),
      createdAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    messages.push(newMessage);
    const limitedMessages = messages.slice(-100);
    await env.BOOKMARKS_KV.put("messages", JSON.stringify(limitedMessages));
    await recordUserMessage(request, env);
    return new Response(JSON.stringify({
      success: true,
      data: newMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to add message"
    }), {
      status: 500,
      headers
    });
  }
}
__name(addMessage, "addMessage");
async function updateMessage(request, env, headers, messageId) {
  try {
    const adminCheck = await verifyAdmin(request, env);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error
      }), {
        status: 401,
        headers
      });
    }
    const messageData = await request.json();
    if (!messageData.name || !messageData.content) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name and content are required"
      }), {
        status: 400,
        headers
      });
    }
    if (messageData.name.length > 20 || messageData.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name or content too long"
      }), {
        status: 400,
        headers
      });
    }
    const existingData = await env.BOOKMARKS_KV.get("messages");
    const messages = existingData ? JSON.parse(existingData) : [];
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messageIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Message not found"
      }), {
        status: 404,
        headers
      });
    }
    const updatedMessage = {
      ...messages[messageIndex],
      name: messageData.name.trim(),
      content: messageData.content.trim(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    messages[messageIndex] = updatedMessage;
    await env.BOOKMARKS_KV.put("messages", JSON.stringify(messages));
    return new Response(JSON.stringify({
      success: true,
      data: updatedMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to update message"
    }), {
      status: 500,
      headers
    });
  }
}
__name(updateMessage, "updateMessage");
async function deleteMessage(request, env, headers, messageId) {
  try {
    const adminCheck = await verifyAdmin(request, env);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error
      }), {
        status: 401,
        headers
      });
    }
    const existingData = await env.BOOKMARKS_KV.get("messages");
    const messages = existingData ? JSON.parse(existingData) : [];
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messageIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Message not found"
      }), {
        status: 404,
        headers
      });
    }
    const deletedMessage = messages[messageIndex];
    messages.splice(messageIndex, 1);
    await env.BOOKMARKS_KV.put("messages", JSON.stringify(messages));
    return new Response(JSON.stringify({
      success: true,
      data: deletedMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to delete message"
    }), {
      status: 500,
      headers
    });
  }
}
__name(deleteMessage, "deleteMessage");
var BANNED_WORDS = [
  // 脏话和骂人的词
  "\u50BB\u903C",
  "\u50BBB",
  "SB",
  "sb",
  "\u8349\u6CE5\u9A6C",
  "\u64CD\u4F60\u5988",
  "\u53BB\u6B7B",
  "\u6B7B\u5168\u5BB6",
  "\u6EDA\u86CB",
  "\u6EDA\u5F00",
  "\u767D\u75F4",
  "\u667A\u969C",
  "\u8111\u6B8B",
  "\u5F31\u667A",
  "\u5783\u573E",
  "\u5E9F\u7269",
  "\u6DF7\u86CB",
  "\u738B\u516B\u86CB",
  "\u72D7\u4E1C\u897F",
  "\u5988\u7684",
  "TMD",
  "tmd",
  "\u4ED6\u5988\u7684",
  "\u4F60\u5988",
  "\u4F60\u7238",
  "\u64CD",
  "\u8279",
  "\u9760",
  "\u5367\u69FD",
  "\u8D31\u4EBA",
  "\u5A4A\u5B50",
  "\u5993\u5973",
  "\u9E21",
  "\u5C4C",
  "\u540A",
  "\u903C",
  "B",
  "\u86CB",
  "\u5C4E",
  "\u5C3F",
  // 政治敏感词
  "\u6CD5\u8F6E\u529F",
  "\u516D\u56DB",
  "\u5929\u5B89\u95E8",
  "\u4E60\u8FD1\u5E73",
  "\u6BDB\u6CFD\u4E1C",
  "\u5171\u4EA7\u515A",
  "\u6C11\u4E3B",
  "\u81EA\u7531",
  // 其他不当内容
  "\u9EC4\u8272",
  "\u8272\u60C5",
  "\u8D4C\u535A",
  "\u6BD2\u54C1",
  "\u81EA\u6740",
  "\u6740\u4EBA",
  "\u7206\u70B8",
  "\u6050\u6016",
  // 网络用语变体
  "cnm",
  "nmsl",
  "wdnmd",
  "rnm",
  "qnm"
];
function containsBannedWords(text) {
  const lowerText = text.toLowerCase();
  for (const word of BANNED_WORDS) {
    if (lowerText.includes(word.toLowerCase())) {
      return {
        hasBanned: true,
        bannedWord: word
      };
    }
  }
  return { hasBanned: false };
}
__name(containsBannedWords, "containsBannedWords");
function getClientIP(request) {
  const cfConnectingIP = request.headers.get("CF-Connecting-IP");
  const xForwardedFor = request.headers.get("X-Forwarded-For");
  const xRealIP = request.headers.get("X-Real-IP");
  if (cfConnectingIP) return cfConnectingIP;
  if (xForwardedFor) return xForwardedFor.split(",")[0].trim();
  if (xRealIP) return xRealIP;
  return "unknown";
}
__name(getClientIP, "getClientIP");
async function checkUserLimits(request, env) {
  const clientIP = getClientIP(request);
  const now = /* @__PURE__ */ new Date();
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1e3);
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1e3);
  const limitsKey = `user_limits_${clientIP}`;
  const limitsData = await env.BOOKMARKS_KV.get(limitsKey);
  const limits = limitsData ? JSON.parse(limitsData) : { messages: [] };
  limits.messages = limits.messages.filter((timestamp) => new Date(timestamp) > oneDayAgo);
  const recentMessages = limits.messages.filter((timestamp) => new Date(timestamp) > oneMinuteAgo);
  if (recentMessages.length >= 1) {
    return {
      allowed: false,
      error: "\u53D1\u8A00\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF71\u5206\u949F\u540E\u518D\u8BD5"
    };
  }
  if (limits.messages.length >= 5) {
    return {
      allowed: false,
      error: "\u4ECA\u65E5\u53D1\u8A00\u6B21\u6570\u5DF2\u8FBE\u4E0A\u9650\uFF085\u6761\uFF09\uFF0C\u8BF7\u660E\u5929\u518D\u8BD5"
    };
  }
  return { allowed: true };
}
__name(checkUserLimits, "checkUserLimits");
async function recordUserMessage(request, env) {
  const clientIP = getClientIP(request);
  const now = (/* @__PURE__ */ new Date()).toISOString();
  const limitsKey = `user_limits_${clientIP}`;
  const limitsData = await env.BOOKMARKS_KV.get(limitsKey);
  const limits = limitsData ? JSON.parse(limitsData) : { messages: [] };
  limits.messages.push(now);
  await env.BOOKMARKS_KV.put(limitsKey, JSON.stringify(limits), {
    expirationTtl: 25 * 60 * 60
    // 25小时
  });
}
__name(recordUserMessage, "recordUserMessage");
async function verifyAdmin(request, env) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { success: false, error: "Missing or invalid authorization header" };
  }
  const token = authHeader.substring(7);
  if (token !== env.ADMIN_PASSWORD) {
    return { success: false, error: "Invalid admin password" };
  }
  return { success: true };
}
__name(verifyAdmin, "verifyAdmin");

// C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-NR7irB/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = worker_default;

// C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-NR7irB/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=worker.js.map
