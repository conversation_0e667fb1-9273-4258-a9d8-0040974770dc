var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-NR7irB/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// src/worker.js
var worker_default = {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization"
    };
    if (request.method === "OPTIONS") {
      return new Response(null, { headers: corsHeaders });
    }
    try {
      if (path.startsWith("/api/bookmarks")) {
        return await handleBookmarksAPI(request, env, corsHeaders);
      }
      if (path.startsWith("/api/messages")) {
        return await handleMessagesAPI(request, env, corsHeaders);
      }
      return new Response("Not Found", { status: 404, headers: corsHeaders });
    } catch (error) {
      console.error("Worker error:", error);
      return new Response(JSON.stringify({ error: "Internal Server Error" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      });
    }
  }
};
async function handleBookmarksAPI(request, env, corsHeaders) {
  const url = new URL(request.url);
  const method = request.method;
  const pathParts = url.pathname.split("/");
  const headers = {
    ...corsHeaders,
    "Content-Type": "application/json"
  };
  switch (method) {
    case "GET":
      if (pathParts[3] === void 0) {
        return await getAllBookmarks(env, headers);
      } else {
        const id = pathParts[3];
        return await getBookmark(env, id, headers);
      }
    case "POST":
      return await addBookmark(request, env, headers);
    case "PUT":
      const updateId = pathParts[3];
      return await updateBookmark(request, env, updateId, headers);
    case "DELETE":
      const deleteId = pathParts[3];
      return await deleteBookmark(request, env, deleteId, headers);
    default:
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers
      });
  }
}
__name(handleBookmarksAPI, "handleBookmarksAPI");
async function getAllBookmarks(env, headers) {
  try {
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    return new Response(JSON.stringify({
      success: true,
      data: bookmarks
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch bookmarks"
    }), {
      status: 500,
      headers
    });
  }
}
__name(getAllBookmarks, "getAllBookmarks");
async function getBookmark(env, id, headers) {
  try {
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmark = bookmarks.find((b) => b.id === parseInt(id));
    if (!bookmark) {
      return new Response(JSON.stringify({
        success: false,
        error: "Bookmark not found"
      }), {
        status: 404,
        headers
      });
    }
    return new Response(JSON.stringify({
      success: true,
      data: bookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(getBookmark, "getBookmark");
async function addBookmark(request, env, headers) {
  try {
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }
    const newBookmark = await request.json();
    if (!newBookmark.name || !newBookmark.url || !newBookmark.description) {
      return new Response(JSON.stringify({
        success: false,
        error: "Missing required fields: name, url, description"
      }), {
        status: 400,
        headers
      });
    }
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const newId = bookmarks.length > 0 ? Math.max(...bookmarks.map((b) => b.id)) + 1 : 1;
    const bookmark = {
      id: newId,
      name: newBookmark.name,
      description: newBookmark.description,
      url: newBookmark.url,
      icon: newBookmark.icon || "\u{1F517}",
      category: newBookmark.category || "\u5176\u4ED6",
      color: newBookmark.color || "#00d4ff",
      createdAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    bookmarks.push(bookmark);
    await env.BOOKMARKS_KV.put("bookmarks", JSON.stringify(bookmarks));
    return new Response(JSON.stringify({
      success: true,
      data: bookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to add bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(addBookmark, "addBookmark");
async function updateBookmark(request, env, id, headers) {
  try {
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }
    const updateData = await request.json();
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmarkIndex = bookmarks.findIndex((b) => b.id === parseInt(id));
    if (bookmarkIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Bookmark not found"
      }), {
        status: 404,
        headers
      });
    }
    bookmarks[bookmarkIndex] = {
      ...bookmarks[bookmarkIndex],
      ...updateData,
      id: parseInt(id),
      // 确保 ID 不被修改
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    await env.BOOKMARKS_KV.put("bookmarks", JSON.stringify(bookmarks));
    return new Response(JSON.stringify({
      success: true,
      data: bookmarks[bookmarkIndex]
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to update bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(updateBookmark, "updateBookmark");
async function deleteBookmark(request, env, id, headers) {
  try {
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }
    const bookmarksData = await env.BOOKMARKS_KV.get("bookmarks");
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmarkIndex = bookmarks.findIndex((b) => b.id === parseInt(id));
    if (bookmarkIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Bookmark not found"
      }), {
        status: 404,
        headers
      });
    }
    const deletedBookmark = bookmarks.splice(bookmarkIndex, 1)[0];
    await env.BOOKMARKS_KV.put("bookmarks", JSON.stringify(bookmarks));
    return new Response(JSON.stringify({
      success: true,
      data: deletedBookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to delete bookmark"
    }), {
      status: 500,
      headers
    });
  }
}
__name(deleteBookmark, "deleteBookmark");
async function handleMessagesAPI(request, env, corsHeaders) {
  const url = new URL(request.url);
  const method = request.method;
  const pathParts = url.pathname.split("/");
  const headers = {
    ...corsHeaders,
    "Content-Type": "application/json"
  };
  const messageId = pathParts[3];
  switch (method) {
    case "GET":
      return await getAllMessages(env, headers);
    case "POST":
      return await addMessage(request, env, headers);
    case "PUT":
      if (!messageId) {
        return new Response(JSON.stringify({ error: "Message ID required" }), {
          status: 400,
          headers
        });
      }
      return await updateMessage(request, env, headers, messageId);
    case "DELETE":
      if (!messageId) {
        return new Response(JSON.stringify({ error: "Message ID required" }), {
          status: 400,
          headers
        });
      }
      return await deleteMessage(request, env, headers, messageId);
    default:
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers
      });
  }
}
__name(handleMessagesAPI, "handleMessagesAPI");
async function getAllMessages(env, headers) {
  try {
    const messagesData = await env.BOOKMARKS_KV.get("messages");
    const messages = messagesData ? JSON.parse(messagesData) : [];
    return new Response(JSON.stringify({
      success: true,
      data: messages
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch messages"
    }), {
      status: 500,
      headers
    });
  }
}
__name(getAllMessages, "getAllMessages");
async function addMessage(request, env, headers) {
  try {
    const messageData = await request.json();
    if (!messageData.name || !messageData.content) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name and content are required"
      }), {
        status: 400,
        headers
      });
    }
    if (messageData.name.length > 20 || messageData.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name or content too long"
      }), {
        status: 400,
        headers
      });
    }
    const existingData = await env.BOOKMARKS_KV.get("messages");
    const messages = existingData ? JSON.parse(existingData) : [];
    const generateId = /* @__PURE__ */ __name(() => {
      return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }, "generateId");
    const newMessage = {
      id: generateId(),
      name: messageData.name.trim(),
      content: messageData.content.trim(),
      createdAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    messages.push(newMessage);
    const limitedMessages = messages.slice(-100);
    await env.BOOKMARKS_KV.put("messages", JSON.stringify(limitedMessages));
    return new Response(JSON.stringify({
      success: true,
      data: newMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to add message"
    }), {
      status: 500,
      headers
    });
  }
}
__name(addMessage, "addMessage");
async function updateMessage(request, env, headers, messageId) {
  try {
    const adminCheck = await verifyAdmin(request, env);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error
      }), {
        status: 401,
        headers
      });
    }
    const messageData = await request.json();
    if (!messageData.name || !messageData.content) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name and content are required"
      }), {
        status: 400,
        headers
      });
    }
    if (messageData.name.length > 20 || messageData.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        error: "Name or content too long"
      }), {
        status: 400,
        headers
      });
    }
    const existingData = await env.BOOKMARKS_KV.get("messages");
    const messages = existingData ? JSON.parse(existingData) : [];
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messageIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Message not found"
      }), {
        status: 404,
        headers
      });
    }
    const updatedMessage = {
      ...messages[messageIndex],
      name: messageData.name.trim(),
      content: messageData.content.trim(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    messages[messageIndex] = updatedMessage;
    await env.BOOKMARKS_KV.put("messages", JSON.stringify(messages));
    return new Response(JSON.stringify({
      success: true,
      data: updatedMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to update message"
    }), {
      status: 500,
      headers
    });
  }
}
__name(updateMessage, "updateMessage");
async function deleteMessage(request, env, headers, messageId) {
  try {
    const adminCheck = await verifyAdmin(request, env);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error
      }), {
        status: 401,
        headers
      });
    }
    const existingData = await env.BOOKMARKS_KV.get("messages");
    const messages = existingData ? JSON.parse(existingData) : [];
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messageIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Message not found"
      }), {
        status: 404,
        headers
      });
    }
    const deletedMessage = messages[messageIndex];
    messages.splice(messageIndex, 1);
    await env.BOOKMARKS_KV.put("messages", JSON.stringify(messages));
    return new Response(JSON.stringify({
      success: true,
      data: deletedMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: "Failed to delete message"
    }), {
      status: 500,
      headers
    });
  }
}
__name(deleteMessage, "deleteMessage");
async function verifyAdmin(request, env) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { success: false, error: "Missing or invalid authorization header" };
  }
  const token = authHeader.substring(7);
  if (token !== env.ADMIN_PASSWORD) {
    return { success: false, error: "Invalid admin password" };
  }
  return { success: true };
}
__name(verifyAdmin, "verifyAdmin");

// C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-NR7irB/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = worker_default;

// C:/Users/<USER>/AppData/Roaming/npm/node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-NR7irB/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=worker.js.map
