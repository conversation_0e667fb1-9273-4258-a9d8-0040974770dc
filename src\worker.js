// <PERSON>flare Worker for Bookmarks API
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // CORS 头设置
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 请求（预检请求）
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      // API 路由
      if (path.startsWith('/api/bookmarks')) {
        return await handleBookmarksAPI(request, env, corsHeaders);
      }

      // 留言 API 路由
      if (path.startsWith('/api/messages')) {
        return await handleMessagesAPI(request, env, corsHeaders);
      }

      return new Response('Not Found', { status: 404, headers: corsHeaders });
    } catch (error) {
      console.error('Worker error:', error);
      return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  }
};

// 处理书签 API 请求
async function handleBookmarksAPI(request, env, corsHeaders) {
  const url = new URL(request.url);
  const method = request.method;
  const pathParts = url.pathname.split('/');

  const headers = {
    ...corsHeaders,
    'Content-Type': 'application/json'
  };

  switch (method) {
    case 'GET':
      // 获取所有书签
      if (pathParts[3] === undefined) {
        return await getAllBookmarks(env, headers);
      }
      // 获取单个书签
      else {
        const id = pathParts[3];
        return await getBookmark(env, id, headers);
      }

    case 'POST':
      // 添加新书签
      return await addBookmark(request, env, headers);

    case 'PUT':
      // 更新书签
      const updateId = pathParts[3];
      return await updateBookmark(request, env, updateId, headers);

    case 'DELETE':
      // 删除书签
      const deleteId = pathParts[3];
      return await deleteBookmark(request, env, deleteId, headers);

    default:
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers
      });
  }
}

// 获取所有书签
async function getAllBookmarks(env, headers) {
  try {
    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];

    return new Response(JSON.stringify({
      success: true,
      data: bookmarks
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to fetch bookmarks'
    }), {
      status: 500,
      headers
    });
  }
}

// 获取单个书签
async function getBookmark(env, id, headers) {
  try {
    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];
    const bookmark = bookmarks.find(b => b.id === parseInt(id));

    if (!bookmark) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Bookmark not found'
      }), {
        status: 404,
        headers
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: bookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to fetch bookmark'
    }), {
      status: 500,
      headers
    });
  }
}

// 添加新书签
async function addBookmark(request, env, headers) {
  try {
    // 验证管理员权限
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }

    const newBookmark = await request.json();

    // 验证必填字段
    if (!newBookmark.name || !newBookmark.url || !newBookmark.description) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing required fields: name, url, description'
      }), {
        status: 400,
        headers
      });
    }

    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];

    // 生成新 ID
    const newId = bookmarks.length > 0 ? Math.max(...bookmarks.map(b => b.id)) + 1 : 1;

    const bookmark = {
      id: newId,
      name: newBookmark.name,
      description: newBookmark.description,
      url: newBookmark.url,
      icon: newBookmark.icon || '🔗',
      category: newBookmark.category || '其他',
      color: newBookmark.color || '#00d4ff',
      createdAt: new Date().toISOString()
    };

    bookmarks.push(bookmark);
    await env.BOOKMARKS_KV.put('bookmarks', JSON.stringify(bookmarks));

    return new Response(JSON.stringify({
      success: true,
      data: bookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to add bookmark'
    }), {
      status: 500,
      headers
    });
  }
}

// 更新书签
async function updateBookmark(request, env, id, headers) {
  try {
    // 验证管理员权限
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }

    const updateData = await request.json();
    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];

    const bookmarkIndex = bookmarks.findIndex(b => b.id === parseInt(id));
    if (bookmarkIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Bookmark not found'
      }), {
        status: 404,
        headers
      });
    }

    // 更新书签数据
    bookmarks[bookmarkIndex] = {
      ...bookmarks[bookmarkIndex],
      ...updateData,
      id: parseInt(id), // 确保 ID 不被修改
      updatedAt: new Date().toISOString()
    };

    await env.BOOKMARKS_KV.put('bookmarks', JSON.stringify(bookmarks));

    return new Response(JSON.stringify({
      success: true,
      data: bookmarks[bookmarkIndex]
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to update bookmark'
    }), {
      status: 500,
      headers
    });
  }
}

// 删除书签
async function deleteBookmark(request, env, id, headers) {
  try {
    // 验证管理员权限
    const authResult = await verifyAdmin(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify(authResult), {
        status: 401,
        headers
      });
    }

    const bookmarksData = await env.BOOKMARKS_KV.get('bookmarks');
    const bookmarks = bookmarksData ? JSON.parse(bookmarksData) : [];

    const bookmarkIndex = bookmarks.findIndex(b => b.id === parseInt(id));
    if (bookmarkIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Bookmark not found'
      }), {
        status: 404,
        headers
      });
    }

    const deletedBookmark = bookmarks.splice(bookmarkIndex, 1)[0];
    await env.BOOKMARKS_KV.put('bookmarks', JSON.stringify(bookmarks));

    return new Response(JSON.stringify({
      success: true,
      data: deletedBookmark
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to delete bookmark'
    }), {
      status: 500,
      headers
    });
  }
}

// 处理留言 API 请求
async function handleMessagesAPI(request, env, corsHeaders) {
  const url = new URL(request.url);
  const method = request.method;
  const pathParts = url.pathname.split('/');

  const headers = {
    ...corsHeaders,
    'Content-Type': 'application/json'
  };

  // 检查是否有消息ID (用于更新和删除)
  const messageId = pathParts[3]; // /api/messages/{id}

  switch (method) {
    case 'GET':
      // 获取所有留言
      return await getAllMessages(env, headers, url);

    case 'POST':
      // 添加新留言
      return await addMessage(request, env, headers);

    case 'PUT':
      // 更新留言 (需要管理员权限)
      if (!messageId) {
        return new Response(JSON.stringify({ error: 'Message ID required' }), {
          status: 400,
          headers
        });
      }
      return await updateMessage(request, env, headers, messageId);

    case 'DELETE':
      // 删除留言 (需要管理员权限)
      if (!messageId) {
        return new Response(JSON.stringify({ error: 'Message ID required' }), {
          status: 400,
          headers
        });
      }
      return await deleteMessage(request, env, headers, messageId);

    default:
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers
      });
  }
}

// 获取所有留言（支持分页）
async function getAllMessages(env, headers, url) {
  try {
    const messagesData = await env.BOOKMARKS_KV.get('messages');
    const allMessages = messagesData ? JSON.parse(messagesData) : [];

    // 获取分页参数
    const searchParams = new URLSearchParams(url.search);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;

    // 计算分页
    const total = allMessages.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    // 按时间倒序排列（最新的在前面）
    const sortedMessages = allMessages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // 获取当前页的数据
    const messages = sortedMessages.slice(startIndex, endIndex);

    return new Response(JSON.stringify({
      success: true,
      data: {
        messages,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to fetch messages'
    }), {
      status: 500,
      headers
    });
  }
}

// 添加新留言
async function addMessage(request, env, headers) {
  try {
    const messageData = await request.json();

    // 验证输入数据
    if (!messageData.name || !messageData.content) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name and content are required'
      }), {
        status: 400,
        headers
      });
    }

    // 限制长度
    if (messageData.name.length > 20 || messageData.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name or content too long'
      }), {
        status: 400,
        headers
      });
    }

    // 检查用户发言限制
    const limitCheck = await checkUserLimits(request, env);
    if (!limitCheck.allowed) {
      return new Response(JSON.stringify({
        success: false,
        error: limitCheck.error
      }), {
        status: 429, // Too Many Requests
        headers
      });
    }

    // 检查违禁词
    const nameCheck = containsBannedWords(messageData.name);
    const contentCheck = containsBannedWords(messageData.content);

    if (nameCheck.hasBanned) {
      return new Response(JSON.stringify({
        success: false,
        error: `姓名包含不当内容，请修改后重试`
      }), {
        status: 400,
        headers
      });
    }

    if (contentCheck.hasBanned) {
      return new Response(JSON.stringify({
        success: false,
        error: `留言内容包含不当内容，请修改后重试`
      }), {
        status: 400,
        headers
      });
    }

    // 获取现有留言
    const existingData = await env.BOOKMARKS_KV.get('messages');
    const messages = existingData ? JSON.parse(existingData) : [];

    // 生成唯一ID
    const generateId = () => {
      return Date.now().toString() + Math.random().toString(36).substring(2, 11);
    };

    // 创建新留言
    const newMessage = {
      id: generateId(),
      name: messageData.name.trim(),
      content: messageData.content.trim(),
      createdAt: new Date().toISOString(),
    };

    messages.push(newMessage);

    // 保存到 KV (只保留最新的 100 条留言)
    const limitedMessages = messages.slice(-100);
    await env.BOOKMARKS_KV.put('messages', JSON.stringify(limitedMessages));

    // 记录用户发言（用于限制检查）
    await recordUserMessage(request, env);

    return new Response(JSON.stringify({
      success: true,
      data: newMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to add message'
    }), {
      status: 500,
      headers
    });
  }
}

// 更新留言
async function updateMessage(request, env, headers, messageId) {
  try {
    // 验证管理员权限
    const adminCheck = await verifyAdmin(request, env);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error
      }), {
        status: 401,
        headers
      });
    }

    const messageData = await request.json();

    // 验证输入数据
    if (!messageData.name || !messageData.content) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name and content are required'
      }), {
        status: 400,
        headers
      });
    }

    // 限制长度
    if (messageData.name.length > 20 || messageData.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name or content too long'
      }), {
        status: 400,
        headers
      });
    }

    // 获取现有留言
    const existingData = await env.BOOKMARKS_KV.get('messages');
    const messages = existingData ? JSON.parse(existingData) : [];

    // 查找要更新的留言
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Message not found'
      }), {
        status: 404,
        headers
      });
    }

    // 更新留言
    const updatedMessage = {
      ...messages[messageIndex],
      name: messageData.name.trim(),
      content: messageData.content.trim(),
      updatedAt: new Date().toISOString()
    };

    messages[messageIndex] = updatedMessage;

    // 保存到 KV
    await env.BOOKMARKS_KV.put('messages', JSON.stringify(messages));

    return new Response(JSON.stringify({
      success: true,
      data: updatedMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to update message'
    }), {
      status: 500,
      headers
    });
  }
}

// 删除留言
async function deleteMessage(request, env, headers, messageId) {
  try {
    // 验证管理员权限
    const adminCheck = await verifyAdmin(request, env);
    if (!adminCheck.success) {
      return new Response(JSON.stringify({
        success: false,
        error: adminCheck.error
      }), {
        status: 401,
        headers
      });
    }

    // 获取现有留言
    const existingData = await env.BOOKMARKS_KV.get('messages');
    const messages = existingData ? JSON.parse(existingData) : [];

    // 查找要删除的留言
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Message not found'
      }), {
        status: 404,
        headers
      });
    }

    // 删除留言
    const deletedMessage = messages[messageIndex];
    messages.splice(messageIndex, 1);

    // 保存到 KV
    await env.BOOKMARKS_KV.put('messages', JSON.stringify(messages));

    return new Response(JSON.stringify({
      success: true,
      data: deletedMessage
    }), { headers });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to delete message'
    }), {
      status: 500,
      headers
    });
  }
}

// 违禁词库
const BANNED_WORDS = [
  // 脏话和骂人的词
  '傻逼', '傻B', 'SB', 'sb', '草泥马', '操你妈', '去死', '死全家', '滚蛋', '滚开',
  '白痴', '智障', '脑残', '弱智', '垃圾', '废物', '混蛋', '王八蛋', '狗东西',
  '妈的', 'TMD', 'tmd', '他妈的', '你妈', '你爸', '操', '艹', '靠', '卧槽',
  '贱人', '婊子', '妓女', '鸡', '屌', '吊', '逼', 'B', '蛋', '屎', '尿',
  // 政治敏感词
  '法轮功', '六四', '天安门', '习近平', '毛泽东', '共产党', '民主', '自由',
  // 其他不当内容
  '黄色', '色情', '赌博', '毒品', '自杀', '杀人', '爆炸', '恐怖',
  // 网络用语变体
  'cnm', 'nmsl', 'wdnmd', 'rnm', 'qnm'
];

// 检查内容是否包含违禁词
function containsBannedWords(text) {
  const lowerText = text.toLowerCase();

  for (const word of BANNED_WORDS) {
    if (lowerText.includes(word.toLowerCase())) {
      return {
        hasBanned: true,
        bannedWord: word
      };
    }
  }

  return { hasBanned: false };
}

// 获取客户端IP地址
function getClientIP(request) {
  // 尝试从不同的头部获取真实IP
  const cfConnectingIP = request.headers.get('CF-Connecting-IP');
  const xForwardedFor = request.headers.get('X-Forwarded-For');
  const xRealIP = request.headers.get('X-Real-IP');

  if (cfConnectingIP) return cfConnectingIP;
  if (xForwardedFor) return xForwardedFor.split(',')[0].trim();
  if (xRealIP) return xRealIP;

  return 'unknown';
}

// 检查用户发言限制
async function checkUserLimits(request, env) {
  const clientIP = getClientIP(request);
  const now = new Date();
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  // 获取用户限制数据
  const limitsKey = `user_limits_${clientIP}`;
  const limitsData = await env.BOOKMARKS_KV.get(limitsKey);
  const limits = limitsData ? JSON.parse(limitsData) : { messages: [] };

  // 清理过期的记录（超过24小时的）
  limits.messages = limits.messages.filter(timestamp => new Date(timestamp) > oneDayAgo);

  // 检查1分钟内的限制
  const recentMessages = limits.messages.filter(timestamp => new Date(timestamp) > oneMinuteAgo);
  if (recentMessages.length >= 1) {
    return {
      allowed: false,
      error: '发言过于频繁，请1分钟后再试'
    };
  }

  // 检查24小时内的限制
  if (limits.messages.length >= 5) {
    return {
      allowed: false,
      error: '今日发言次数已达上限（5条），请明天再试'
    };
  }

  return { allowed: true };
}

// 记录用户发言
async function recordUserMessage(request, env) {
  const clientIP = getClientIP(request);
  const now = new Date().toISOString();

  const limitsKey = `user_limits_${clientIP}`;
  const limitsData = await env.BOOKMARKS_KV.get(limitsKey);
  const limits = limitsData ? JSON.parse(limitsData) : { messages: [] };

  // 添加当前发言时间
  limits.messages.push(now);

  // 保存到KV（设置25小时过期，确保数据自动清理）
  await env.BOOKMARKS_KV.put(limitsKey, JSON.stringify(limits), {
    expirationTtl: 25 * 60 * 60 // 25小时
  });
}

// 验证管理员权限
async function verifyAdmin(request, env) {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { success: false, error: 'Missing or invalid authorization header' };
  }

  const token = authHeader.substring(7); // 移除 "Bearer " 前缀

  if (token !== env.ADMIN_PASSWORD) {
    return { success: false, error: 'Invalid admin password' };
  }

  return { success: true };
}
